const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const {
  getTestsByLoincNums,
  getAllLabTestsQuery,
  searchLabTestsQuery,
  getAllDistinctClassValuesQuery,
  getOrganizationTestIdsWithPricingQuery,
  searchLabTestsByIdsQuery,
  searchLabTestsOptimizedQuery,
} = require('../queries/lab-test-query')
const logging = require('../common/logging')
const {
  classToDepartmentMapping,
  getDepartmentByClassCode,
} = require('../common/class-department-mapping')
const labTestContainer = 'lab_tests'
const organizationTestsContainer = 'OrganizationTests'

const {
  sanitizeInput,
  sanitizeSearchText,
  escapeForCosmosDB,
} = require('../utils/sanitization')
const searchCache = require('../utils/search-cache')

class TestRepository {
  async findByLoincNums(loincNums) {
    try {
      const query = getTestsByLoincNums(loincNums)
      return cosmosDbContext.queryItems(query, labTestContainer)
    } catch (error) {
      logging.logError('Error fetching lab tests by LOINC numbers', error)
      throw new Error('Failed to fetch lab tests by LOINC numbers')
    }
  }

  async bulkInsertTest(tests) {
    try {
      const insertResults = []
      for (const test of tests) {
        const result = await cosmosDbContext.createItem(test, labTestContainer)
        insertResults.push(result)
      }
      return insertResults
    } catch (error) {
      logging.logError('Error inserting lab tests', error)
      throw new Error('Failed to insert lab tests')
    }
  }

  async getAllLabTests() {
    try {
      const query = getAllLabTestsQuery()
      return cosmosDbContext.queryItems(query, labTestContainer)
    } catch (error) {
      logging.logError('Error fetching all lab tests', error)
      throw new Error('Failed to fetch lab tests')
    }
  }

  async searchLabTests(
    searchText,
    pageSize = 10,
    continuationToken = null,
    department,
  ) {
    try {
      function sortSearchResults(items, searchText) {
        if (!searchText) return items
        const lowerSearch = searchText.toLowerCase()

        return items.sort((a, b) => {
          const aName =
            a.DisplayName?.toLowerCase() ||
            a.SHORTNAME?.toLowerCase() ||
            a.LONG_COMMON_NAME?.toLowerCase() ||
            ''
          const bName =
            b.DisplayName?.toLowerCase() ||
            b.SHORTNAME?.toLowerCase() ||
            b.LONG_COMMON_NAME?.toLowerCase() ||
            ''

          const aStarts = aName.startsWith(lowerSearch) ? 0 : 1
          const bStarts = bName.startsWith(lowerSearch) ? 0 : 1

          if (aStarts !== bStarts) return aStarts - bStarts
          return aName.localeCompare(bName)
        })
      }

      let classFilter = []
      const fetchSize = 5000
      if (department === 'ALL') {
        classFilter = null
      } else if (department === 'OTHERS') {
        const allClasses = await this.getAllClassValues()
        const knownClasses = Object.keys(classToDepartmentMapping)
        classFilter = allClasses.filter((cls) => !knownClasses.includes(cls))
      } else {
        classFilter = Object.entries(classToDepartmentMapping)
          .filter(([, dept]) => dept === department)
          .map(([classCode]) => classCode)
      }

      const query = searchLabTestsQuery(searchText, classFilter)

      const data = await cosmosDbContext.getAllItemQuery(
        labTestContainer,
        query,
        fetchSize,
        continuationToken,
      )

      // If no results but there's a continuation token, try fetching next page
      if (data.items.length === 0 && data.nextToken) {
        const nextPageData = await cosmosDbContext.getAllItemQuery(
          labTestContainer,
          query,
          fetchSize,
          data.nextToken,
        )

        // If we found results on the next page, use those
        if (nextPageData.items.length > 0) {
          data.items = nextPageData.items
          data.nextToken = nextPageData.nextToken
        }
      }

      // If still no results, try a simpler query as fallback
      if (data.items.length === 0 && searchText) {
        const simpleQuery = `SELECT * FROM c WHERE CONTAINS(UPPER(c.DisplayName), UPPER("${searchText.replace(
          /"/g,
          '',
        )}")) OR CONTAINS(UPPER(c.SHORTNAME), UPPER("${searchText.replace(
          /"/g,
          '',
        )}")) OR CONTAINS(UPPER(c.LONG_COMMON_NAME), UPPER("${searchText.replace(
          /"/g,
          '',
        )}"))`

        const simpleData = await cosmosDbContext.getAllItemQuery(
          labTestContainer,
          simpleQuery,
          fetchSize,
          null,
        )

        if (simpleData.items.length > 0) {
          data.items = simpleData.items
          data.nextToken = simpleData.nextToken
        }
      }

      const sortedItems = sortSearchResults(data.items, searchText)

      return {
        items: sortedItems,
        continuationToken: data.continuationToken,
      }
    } catch (error) {
      logging.logError(`Unable to search test with query`, error)
      return { items: [], nextToken: null }
    }
  }
  async getAllClassValues() {
    try {
      const query = getAllDistinctClassValuesQuery()
      const result = await cosmosDbContext.queryItems(query, labTestContainer)

      return result.map((item) => item.CLASS)
    } catch (error) {
      console.error('Error fetching class values from Cosmos DB:', error)
      throw new Error('Failed to fetch class values')
    }
  }

  async fetchLoincList(
    searchText,
    department,
    organizationId,
    pageSize = 100,
    continuationToken = null,
    page = 1,
    status = null,
  ) {
    try {
      const sanitizedSearchText = sanitizeSearchText(searchText)
      const sanitizedDepartment = sanitizeInput(department)

      let actualPage = page
      if (continuationToken && continuationToken.startsWith('page_')) {
        actualPage = parseInt(continuationToken.replace('page_', ''))
      }

      // Handle status-based filtering
      if (status === 'active') {
        // Only show tests from organization container that are active
        return await this.fetchActiveOrganizationTests(
          organizationId,
          sanitizedSearchText,
          sanitizedDepartment,
          pageSize,
          actualPage,
        )
      } else if (status === 'inactive') {
        // Only show tests from lab_tests container that are NOT in organization container
        return await this.fetchInactiveTests(
          organizationId,
          sanitizedSearchText,
          sanitizedDepartment,
          pageSize,
          actualPage,
        )
      }

      // Default behavior: show all tests with organization data merged
      // Step 1: Get organization tests map for efficient lookup (only once)
      const orgTestsMap = await this.getOrganizationTestsMap(organizationId)

      const countQuery = this.buildCountQuery(
        sanitizedSearchText,
        sanitizedDepartment,
      )

      const countResult = await cosmosDbContext.queryItems(
        countQuery,
        labTestContainer,
      )
      const totalCount = countResult[0] || 0
      const totalPages = Math.ceil(totalCount / pageSize)

      const loincQuery = this.buildOptimizedLoincQuery(
        sanitizedSearchText,
        sanitizedDepartment,
        pageSize,
        actualPage,
      )

      const allResults = await cosmosDbContext.queryItems(
        loincQuery,
        labTestContainer,
      )

      const hasMoreResults = allResults.length > pageSize
      const actualResults = hasMoreResults
        ? allResults.slice(0, pageSize)
        : allResults

      const nextPageToken = hasMoreResults ? `page_${actualPage + 1}` : null

      const transformedItems = actualResults.map((loincTest, index) => {
        const orgData = orgTestsMap.get(loincTest.id) || {
          isActive: false,
          price: 0,
        }

        return {
          loincNum: loincTest.id,
          class: loincTest.CLASS,
          shortName: loincTest.SHORTNAME,
          longCommonName: loincTest.LONG_COMMON_NAME,
          displayName: loincTest.DisplayName,
          isActive: orgData.isActive,
          cost: 0,
          organizationCost: orgData.price,
        }
      })

      const response = {
        items: transformedItems,
        continuationToken: nextPageToken,
        hasMoreResults: hasMoreResults,
        currentPage: actualPage,
        pageSize: pageSize,
        totalFetched: actualResults.length,
        totalCount: totalCount,
        totalPages: totalPages,
      }

      return response
    } catch (error) {
      logging.logError('Error fetching LOINC list:', error)
      throw new Error('Failed to fetch LOINC list')
    }
  }

  buildQueryConditions(searchText, department) {
    const conditions = []

    if (searchText && searchText.trim() !== '') {
      // Use improved sanitization for search text
      const cleanSearchText = sanitizeSearchText(searchText)
      if (cleanSearchText) {
        // Escape the search text for safe use in Cosmos DB queries
        const escapedSearchText = escapeForCosmosDB(cleanSearchText)

        // Optimized search: prioritize STARTSWITH for better performance
        const lowerSearchText = escapedSearchText.toLowerCase()
        const searchCondition = `(
          STARTSWITH(LOWER(c.DisplayName), "${lowerSearchText}") OR
          STARTSWITH(LOWER(c.SHORTNAME), "${lowerSearchText}") OR
          STARTSWITH(LOWER(c.LONG_COMMON_NAME), "${lowerSearchText}") OR
          STARTSWITH(LOWER(c.id), "${lowerSearchText}") OR
          CONTAINS(LOWER(c.DisplayName), "${lowerSearchText}") OR
          CONTAINS(LOWER(c.SHORTNAME), "${lowerSearchText}") OR
          CONTAINS(LOWER(c.LONG_COMMON_NAME), "${lowerSearchText}")
        )`
        conditions.push(searchCondition)
      }
    }

    if (department && department !== 'ALL') {
      if (department === 'Others' || department === 'OTHERS') {
        const knownClasses = Object.keys(classToDepartmentMapping)
        const excludeCondition = knownClasses
          .map((cls) => `c.CLASS != "${cls}"`)
          .join(' AND ')
        conditions.push(`(${excludeCondition})`)
      } else {
        const departmentClasses = Object.entries(classToDepartmentMapping)
          .filter(([, dept]) => dept === department)
          .map(([classCode]) => classCode)

        if (departmentClasses.length > 0) {
          const classCondition = departmentClasses
            .map((cls) => `c.CLASS = "${cls}"`)
            .join(' OR ')
          conditions.push(`(${classCondition})`)
        }
      }
    }

    return conditions
  }

  // Build count query to get total number of records
  buildCountQuery(searchText, department) {
    let query = `SELECT VALUE COUNT(1) FROM c`
    const conditions = this.buildQueryConditions(searchText, department)

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    return query
  }

  buildOptimizedLoincQuery(searchText, department, pageSize, page) {
    // Use optimized field selection and ordering for better performance
    let query = `SELECT c.id, c.CLASS, c.SHORTNAME, c.LONG_COMMON_NAME, c.DisplayName FROM c`
    const conditions = this.buildQueryConditions(searchText, department)

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    // Optimize ordering: use indexed field if available, otherwise use id
    if (searchText && searchText.trim()) {
      // For search queries, order by relevance (DisplayName first, then others)
      query += ` ORDER BY c.DisplayName, c.id`
    } else {
      query += ` ORDER BY c.id`
    }

    const offset = (page - 1) * pageSize
    query += ` OFFSET ${offset} LIMIT ${pageSize + 1}` // +1 to check if there are more results

    return query
  }

  async getOrganizationTestsMap(organizationId) {
    try {
      // Optimized query with only required fields
      const query = `SELECT c.testId, c.isActive, c.price FROM c WHERE c.organizationId = "${organizationId}"`
      const orgTests = await cosmosDbContext.queryItems(
        query,
        organizationTestsContainer,
      )

      if (orgTests.length > 0) {
        console.log(
          `Loaded ${orgTests.length} organization tests for org: ${organizationId}`,
        )
      }

      const orgTestsMap = new Map()
      orgTests.forEach((test) => {
        orgTestsMap.set(test.testId, {
          isActive: test.isActive || false,
          price: test.price || 0,
        })
      })
      return orgTestsMap
    } catch (error) {
      logging.logError('Error fetching organization tests map:', error)
      return new Map()
    }
  }

  async searchOrganizationLabTestsOptimized(
    organizationId,
    searchText,
    pageSize,
    continuationToken,
    department,
  ) {
    try {
      const startTime = Date.now()

      if (searchCache.shouldCache(searchText)) {
        const cachedResult = searchCache.get(
          organizationId,
          searchText,
          department,
          pageSize,
          continuationToken,
        )
        if (cachedResult) {
          return cachedResult
        }
      }
      const normalizedSearchText = searchText.trim().replace(/\s+/g, ' ')


      // Step 1: Build optimized search query with limited fields
      const sanitizedSearchText = sanitizeSearchText(normalizedSearchText)
      const sanitizedDepartment = sanitizeInput(department)

      let query = `SELECT c.id, c.CLASS, c.SHORTNAME, c.LONG_COMMON_NAME, c.DisplayName FROM c`
      const conditions = []

      // Optimized search conditions with better matching strategy
      if (sanitizedSearchText && sanitizedSearchText.trim()) {
        const escapedSearchText =
          escapeForCosmosDB(sanitizedSearchText).toLowerCase()

        // Priority: STARTSWITH first (exact prefix matches), then CONTAINS (substring matches)
        const searchCondition = `(
          STARTSWITH(LOWER(c.DisplayName), "${escapedSearchText}") OR
          STARTSWITH(LOWER(c.SHORTNAME), "${escapedSearchText}") OR
          STARTSWITH(LOWER(c.LONG_COMMON_NAME), "${escapedSearchText}") OR
          STARTSWITH(LOWER(c.id), "${escapedSearchText}") OR
          CONTAINS(LOWER(c.DisplayName), "${escapedSearchText}") OR
          CONTAINS(LOWER(c.SHORTNAME), "${escapedSearchText}") OR
          CONTAINS(LOWER(c.LONG_COMMON_NAME), "${escapedSearchText}") OR
          CONTAINS(LOWER(c.id), "${escapedSearchText}")
        )`
        conditions.push(searchCondition)
      }

      if (sanitizedDepartment && sanitizedDepartment !== 'ALL') {
        if (
          sanitizedDepartment === 'Others' ||
          sanitizedDepartment === 'OTHERS'
        ) {
          const knownClasses = Object.keys(classToDepartmentMapping)
          const excludeCondition = knownClasses
            .map((cls) => `c.CLASS != "${cls}"`)
            .join(' AND ')
          conditions.push(`(${excludeCondition})`)
        } else {
          const departmentClasses = Object.entries(classToDepartmentMapping)
            .filter(([, dept]) => dept === sanitizedDepartment)
            .map(([classCode]) => classCode)

          if (departmentClasses.length > 0) {
            const classCondition = departmentClasses
              .map((cls) => `c.CLASS = "${cls}"`)
              .join(' OR ')
            conditions.push(`(${classCondition})`)
          }
        }
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`
      }

      // Simple ordering - Cosmos DB doesn't support complex CASE in ORDER BY
      query += ` ORDER BY c.DisplayName`

      // Step 2: Execute search with pagination
      const searchResult = await cosmosDbContext.getAllItemQuery(
        labTestContainer,
        query,
        pageSize || 1000,
        continuationToken,
      )

      // Step 3: Get organization tests map (cached if possible)
      const orgTestsMap = await this.getOrganizationTestsMap(organizationId)

      // Step 4: Transform results with organization data
      const transformedItems = searchResult.items.map((loincTest) => {
        const orgData = orgTestsMap.get(loincTest.id) || {
          isActive: false,
          price: 0,
        }

        return {
          loincNum: loincTest.id,
          class: loincTest.CLASS,
          shortName: loincTest.SHORTNAME,
          longCommonName: loincTest.LONG_COMMON_NAME,
          displayName: loincTest.DisplayName,
          isActive: orgData.isActive,
          cost: 0,
          organizationCost: orgData.price,
        }
      })

      // Step 5: Client-side sorting for relevance (since Cosmos DB ORDER BY is limited)
      if (sanitizedSearchText && sanitizedSearchText.trim()) {
        const lowerSearchText = sanitizedSearchText.toLowerCase()
        transformedItems.sort((a, b) => {
          // Priority scoring: lower score = higher priority
          const getScore = (item) => {
            const displayName = (item.displayName || '').toLowerCase()
            const shortName = (item.shortName || '').toLowerCase()
            const longName = (item.longCommonName || '').toLowerCase()

            if (displayName.startsWith(lowerSearchText)) return 1
            if (shortName.startsWith(lowerSearchText)) return 2
            if (longName.startsWith(lowerSearchText)) return 3
            if (displayName.includes(lowerSearchText)) return 4
            if (shortName.includes(lowerSearchText)) return 5
            if (longName.includes(lowerSearchText)) return 6
            return 7
          }

          const scoreA = getScore(a)
          const scoreB = getScore(b)

          if (scoreA !== scoreB) return scoreA - scoreB

          // If same score, sort alphabetically by display name
          return (a.displayName || '').localeCompare(b.displayName || '')
        })
      }

      const endTime = Date.now()

      const result = {
        items: transformedItems,
        continuationToken: searchResult.continuationToken,
        hasMoreResults: !!searchResult.continuationToken,
        totalFetched: transformedItems.length,
      }

      // Cache the result if appropriate
      if (searchCache.shouldCache(searchText)) {
        searchCache.set(
          organizationId,
          searchText,
          department,
          pageSize,
          continuationToken,
          result,
        )
      }

      return result
    } catch (error) {
      console.error('Error in optimized organization lab tests search:', error)
      // Return empty result with 200 status code to match medicine search behavior
      return {
        items: [],
        continuationToken: null,
        hasMoreResults: false,
        totalFetched: 0
      }
    }
  }

  async bulkInsertLoincData(data) {
    try {
      const results = []
      for (const item of data) {
        const result = await cosmosDbContext.createItem(item, labTestContainer)
        results.push(result)
      }
      return results
    } catch (error) {
      logging.logError('Error inserting LOINC data', error)
      throw new Error('Failed to insert LOINC data')
    }
  }

  async updateOrganizationTests(
    organizationId,
    tests,
    department = null,
    selectAll = false,
  ) {
    try {
      const updatedTests = []

      let testsToUpdate = tests

      if (selectAll && department) {
        logging.logInfo(
          'Fetching tests for selectAll and department using optimized query',
        )

        let optimizedQuery

        if (department === 'Others') {
          // For "Others" department, get all tests that don't belong to mapped departments
          const mappedClasses = Object.keys(classToDepartmentMapping)
          const classCondition = mappedClasses
            .map((cls) => `c.CLASS != "${cls}"`)
            .join(' AND ')

          optimizedQuery = `SELECT c.id FROM c WHERE (${classCondition})`
        } else {
          // For mapped departments, get tests that belong to those classes
          const departmentClasses = Object.entries(classToDepartmentMapping)
            .filter(([, dept]) => dept === department)
            .map(([classCode]) => classCode)

          if (departmentClasses.length === 0) {
            throw new Error(`No classes found for department: ${department}`)
          }

          const classCondition = departmentClasses
            .map((cls) => `c.CLASS = "${cls}"`)
            .join(' OR ')

          optimizedQuery = `SELECT c.id FROM c WHERE (${classCondition})`
        }

        console.log(`Optimized selectAll query: ${optimizedQuery}`)

        const allTests = await cosmosDbContext.queryItems(
          optimizedQuery,
          labTestContainer,
        )

        console.log(
          `Found ${allTests.length} tests for department ${department}`,
        )

        testsToUpdate = allTests.map((test) => ({
          testId: test.id,
          isActive: true,
        }))
      }

      const existingTestsQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
      const existingTests = await cosmosDbContext.queryItems(
        existingTestsQuery,
        organizationTestsContainer,
      )
      const existingTestIds = new Set(existingTests.map((test) => test.testId))

      const newTests = []
      const updatedExistingTests = []

      for (const test of testsToUpdate) {
        const { testId, isActive, price } = test

        if (existingTestIds.has(testId)) {
          const existingTest = existingTests.find((t) => t.testId === testId)
          existingTest.isActive = isActive
          if (!selectAll) {
            existingTest.price = price // Update price only when selectAll is false
          }
          existingTest.updatedOn = new Date().toISOString()
          updatedExistingTests.push(existingTest)
        } else {
          const newTest = {
            organizationId,
            testId,
            isActive: true,
            price: price || 0,
            departments: [],
            createdOn: new Date().toISOString(),
            updatedOn: new Date().toISOString(),
          }
          newTests.push(newTest)
        }
      }

      const envConfig = cosmosDbContext.getEnvironmentConfig()
      const batchSize = envConfig.batchSize // 500 for dev, 1000 for prod
      const maxConcurrency = envConfig.maxConcurrency // 6 for dev, 10 for prod

      logging.logInfo(
        `Using ${envConfig.environment} config: batchSize=${batchSize}, maxConcurrency=${maxConcurrency}`,
      )

      logging.logInfo(
        `Processing ${updatedExistingTests.length} existing tests in optimized batches`,
      )

      // Process existing tests updates in optimized batches
      for (let i = 0; i < updatedExistingTests.length; i += batchSize) {
        const batch = updatedExistingTests.slice(i, i + batchSize)

        // Process in smaller concurrent chunks to avoid rate limits
        const chunks = []
        const chunkSize = Math.ceil(batch.length / maxConcurrency)

        for (let j = 0; j < batch.length; j += chunkSize) {
          chunks.push(batch.slice(j, j + chunkSize))
        }

        await Promise.all(
          chunks.map(async (chunk) => {
            await Promise.all(
              chunk.map((test) =>
                cosmosDbContext.updateItem(test, organizationTestsContainer),
              ),
            )
          }),
        )

        updatedTests.push(...batch)
        console.log(
          `Processed ${Math.min(i + batchSize, updatedExistingTests.length)}/${
            updatedExistingTests.length
          } existing tests`,
        )

        // Add delay between batches to avoid metadata-429 errors
        if (i + batchSize < updatedExistingTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200)) // 200ms delay for metadata safety
        }
      }

      logging.logInfo(
        `Processing ${newTests.length} new tests in optimized batches`,
      )

      // Process new tests in optimized batches
      for (let i = 0; i < newTests.length; i += batchSize) {
        const batch = newTests.slice(i, i + batchSize)

        // Process in smaller concurrent chunks to avoid rate limits
        const chunks = []
        const chunkSize = Math.ceil(batch.length / maxConcurrency)

        for (let j = 0; j < batch.length; j += chunkSize) {
          chunks.push(batch.slice(j, j + chunkSize))
        }

        await Promise.all(
          chunks.map(async (chunk) => {
            await Promise.all(
              chunk.map((test) =>
                cosmosDbContext.createItem(test, organizationTestsContainer),
              ),
            )
          }),
        )

        updatedTests.push(...batch)
        console.log(
          `Processed ${Math.min(i + batchSize, newTests.length)}/${
            newTests.length
          } new tests`,
        )

        // Add delay between batches to avoid metadata-429 errors
        if (i + batchSize < newTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200)) // 200ms delay for metadata safety
        }
      }

      logging.logInfo('Finished updateOrganizationTests method')
      return updatedTests
    } catch (error) {
      logging.logError('Error updating organization test details', error)
      throw new Error('Failed to update organization test details')
    }
  }

  // Optimized async version with progress tracking
  async updateOrganizationTestsAsync(
    organizationId,
    tests,
    department = null,
    selectAll = false,
    progressCallback = null,
  ) {
    try {
      const updatedTests = []
      let testsToUpdate = tests

      // Step 1: Get tests to update (optimized for selectAll)
      if (selectAll && department) {
        if (progressCallback)
          progressCallback(0, 0, 'Fetching tests for department...')

        let optimizedQuery

        if (department === 'Others') {
          // For "Others" department, get all tests that don't belong to mapped departments
          const mappedClasses = Object.keys(classToDepartmentMapping)
          const classCondition = mappedClasses
            .map((cls) => `c.CLASS != "${cls}"`)
            .join(' AND ')

          optimizedQuery = `SELECT c.id FROM c WHERE (${classCondition})`
        } else {
          // For mapped departments, get tests that belong to those classes
          const departmentClasses = Object.entries(classToDepartmentMapping)
            .filter(([, dept]) => dept === department)
            .map(([classCode]) => classCode)

          if (departmentClasses.length === 0) {
            throw new Error(`No classes found for department: ${department}`)
          }

          const classCondition = departmentClasses
            .map((cls) => `c.CLASS = "${cls}"`)
            .join(' OR ')

          optimizedQuery = `SELECT c.id FROM c WHERE (${classCondition})`
        }

        const allTests = await cosmosDbContext.queryItems(
          optimizedQuery,
          labTestContainer,
        )

        testsToUpdate = allTests.map((test) => ({
          testId: test.id,
          isActive: true,
        }))

        if (progressCallback) {
          progressCallback(
            0,
            testsToUpdate.length,
            `Found ${testsToUpdate.length} tests for ${department}`,
          )
        }
      }

      // Step 2: Get existing tests
      if (progressCallback)
        progressCallback(
          0,
          testsToUpdate.length,
          'Loading existing organization tests...',
        )

      const existingTests = await cosmosDbContext.queryItems(
        `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`,
        organizationTestsContainer,
      )
      const existingTestIds = new Set(existingTests.map((test) => test.testId))

      // Step 3: Categorize tests
      const newTests = []
      const updatedExistingTests = []

      for (const test of testsToUpdate) {
        const { testId, isActive, price } = test

        if (existingTestIds.has(testId)) {
          const existingTest = existingTests.find((t) => t.testId === testId)
          existingTest.isActive = isActive
          if (!selectAll) {
            existingTest.price = price
          }
          existingTest.updatedOn = new Date().toISOString()
          updatedExistingTests.push(existingTest)
        } else {
          const newTest = {
            organizationId,
            testId,
            isActive: true,
            price: price || 0,
            departments: [],
            createdOn: new Date().toISOString(),
            updatedOn: new Date().toISOString(),
          }
          newTests.push(newTest)
        }
      }

      const totalOperations = updatedExistingTests.length + newTests.length
      let processedOperations = 0

      // Step 4: Process existing tests with environment-aware configuration
      const envConfig = cosmosDbContext.getEnvironmentConfig()
      const batchSize = envConfig.batchSize // 500 for dev, 1000 for prod
      const maxConcurrency = envConfig.maxConcurrency // 6 for dev, 10 for prod

      logging.logInfo(
        `Async processing with ${envConfig.environment} config: batchSize=${batchSize}, maxConcurrency=${maxConcurrency}`,
      )

      for (let i = 0; i < updatedExistingTests.length; i += batchSize) {
        const batch = updatedExistingTests.slice(i, i + batchSize)

        const chunks = []
        const chunkSize = Math.ceil(batch.length / maxConcurrency)

        for (let j = 0; j < batch.length; j += chunkSize) {
          chunks.push(batch.slice(j, j + chunkSize))
        }

        await Promise.all(
          chunks.map(async (chunk) => {
            await Promise.all(
              chunk.map((test) =>
                cosmosDbContext.updateItem(test, organizationTestsContainer),
              ),
            )
          }),
        )

        updatedTests.push(...batch)
        processedOperations += batch.length

        if (progressCallback) {
          progressCallback(
            processedOperations,
            totalOperations,
            `Updated ${processedOperations}/${totalOperations} tests`,
          )
        }

        // Add delay between batches to avoid metadata-429 errors
        if (i + batchSize < updatedExistingTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200))
        }
      }

      // Step 5: Process new tests with progress tracking
      for (let i = 0; i < newTests.length; i += batchSize) {
        const batch = newTests.slice(i, i + batchSize)

        const chunks = []
        const chunkSize = Math.ceil(batch.length / maxConcurrency)

        for (let j = 0; j < batch.length; j += chunkSize) {
          chunks.push(batch.slice(j, j + chunkSize))
        }

        await Promise.all(
          chunks.map(async (chunk) => {
            await Promise.all(
              chunk.map((test) =>
                cosmosDbContext.createItem(test, organizationTestsContainer),
              ),
            )
          }),
        )

        updatedTests.push(...batch)
        processedOperations += batch.length

        if (progressCallback) {
          progressCallback(
            processedOperations,
            totalOperations,
            `Processed ${processedOperations}/${totalOperations} tests`,
          )
        }

        // Add delay between batches to avoid metadata-429 errors
        if (i + batchSize < newTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200))
        }
      }

      if (progressCallback) {
        progressCallback(
          totalOperations,
          totalOperations,
          'All tests processed successfully!',
        )
      }

      return {
        totalProcessed: updatedTests.length,
        newTests: newTests.length,
        updatedTests: updatedExistingTests.length,
        tests: updatedTests,
      }
    } catch (error) {
      logging.logError('Error in async organization tests update:', error)
      throw error
    }
  }

  async fetchLoincTestsForOrganization(organizationId) {
    try {
      const loincTestsQuery = `SELECT * FROM c`
      const loincTests = await cosmosDbContext.queryItems(
        loincTestsQuery,
        labTestContainer,
      )

      const organizationTests = await cosmosDbContext.queryItems(
        `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`,
        organizationTestsContainer,
      )

      const mergedTests = loincTests.map((loincTest) => {
        const orgTest = organizationTests.find(
          (test) => test.testId === loincTest.id,
        )
        return {
          ...loincTest,
          isActive: orgTest?.isActive || false,
          price: orgTest?.price || null,
          departments: orgTest?.departments || [],
        }
      })

      return mergedTests
    } catch (error) {
      logging.logError('Error fetching LOINC tests for organization', error)
      throw new Error('Failed to fetch LOINC tests for organization')
    }
  }

  async getOrganizationTestIdsWithPricing(organizationId) {
    try {
      const query = getOrganizationTestIdsWithPricingQuery(organizationId)

      const result = await cosmosDbContext.queryItems(
        query,
        organizationTestsContainer,
      )
      return result
    } catch (error) {
      logging.logError(
        `Error fetching organization test IDs with pricing for org ${organizationId}:`,
        error,
      )
      return []
    }
  }

  async searchLabTestsByIds(
    testIds,
    searchText,
    classFilter,
    pageSize,
    continuationToken,
  ) {
    try {
      const queryResult = searchLabTestsByIdsQuery(
        testIds,
        searchText,
        classFilter,
      )

      // Check if we need batch processing for large test ID arrays
      if (
        typeof queryResult === 'object' &&
        queryResult.requiresBatchProcessing
      ) {
        console.log(
          `Large test ID array detected (${testIds.length} tests). Using optimized search.`,
        )
        return await this.searchLabTestsOptimized(
          testIds,
          searchText,
          classFilter,
          pageSize,
          continuationToken,
        )
      }

      const result = await cosmosDbContext.getAllItemQuery(
        labTestContainer,
        queryResult,
        pageSize,
        continuationToken,
      )

      return result
    } catch (error) {
      logging.logError(
        `Error searching lab tests by IDs with search text "${searchText}":`,
        error,
      )
      return { items: [], continuationToken: null }
    }
  }

  async searchLabTestsOptimized(
    testIds,
    searchText,
    classFilter,
    pageSize,
    continuationToken,
  ) {
    try {
      const batchSize = 500
      const allResults = []
      let totalProcessed = 0

      for (let i = 0; i < testIds.length; i += batchSize) {
        const batch = testIds.slice(i, i + batchSize)
        console.log(
          `Processing batch ${Math.floor(i / batchSize) + 1}: ${
            batch.length
          } test IDs`,
        )

        try {
          const batchQuery = searchLabTestsByIdsQuery(
            batch,
            searchText,
            classFilter,
          )

          const batchResult = await cosmosDbContext.getAllItemQuery(
            labTestContainer,
            batchQuery,
            batchSize,
            null,
          )

          if (batchResult.items && batchResult.items.length > 0) {
            allResults.push(...batchResult.items)
            console.log(
              `Batch ${Math.floor(i / batchSize) + 1} returned ${
                batchResult.items.length
              } results`,
            )
          }

          totalProcessed += batch.length
        } catch (batchError) {
          console.error(
            `Error processing batch ${Math.floor(i / batchSize) + 1}:`,
            batchError,
          )
        }
      }

      // Sorting logic to prioritize items starting with the search text
      const lowerSearchText = searchText.toLowerCase()
      const sortedResults = allResults.sort((a, b) => {
        const aName =
          a.DisplayName?.toLowerCase() ||
          a.SHORTNAME?.toLowerCase() ||
          a.LONG_COMMON_NAME?.toLowerCase() ||
          ''
        const bName =
          b.DisplayName?.toLowerCase() ||
          b.SHORTNAME?.toLowerCase() ||
          b.LONG_COMMON_NAME?.toLowerCase() ||
          ''

        const aStartsWith = aName.startsWith(lowerSearchText) ? 0 : 1
        const bStartsWith = bName.startsWith(lowerSearchText) ? 0 : 1

        if (aStartsWith !== bStartsWith) return aStartsWith - bStartsWith
        return aName.localeCompare(bName)
      })

      const startIndex = 0
      const endIndex = Math.min(pageSize || 1000, sortedResults.length)
      const paginatedItems = sortedResults.slice(startIndex, endIndex)

      const hasMoreResults = sortedResults.length > endIndex

      return {
        items: paginatedItems,
        continuationToken: hasMoreResults ? 'batch_more' : null,
      }
    } catch (error) {
      logging.logError(
        `Error in optimized batch search for lab tests with search text "${searchText}":`,
        error,
      )
      return { items: [], continuationToken: null }
    }
  }

  async removeOrganizationTests(
    organizationId,
    tests,
    department = null,
    selectAll = false,
  ) {
    try {
      const removedTests = []
      let testsToRemove = tests

      if (selectAll && department) {
        // Get all organization tests first with batching to avoid rate limiting
        const allOrgTestsQuery = `SELECT c.testId FROM c WHERE c.organizationId = "${organizationId}"`
        const allOrgTests = await cosmosDbContext.queryItems(
          allOrgTestsQuery,
          organizationTestsContainer,
        )

        if (allOrgTests.length === 0) {
          testsToRemove = []
        } else {
          // Process test IDs in smaller batches to avoid rate limiting
          const testIds = allOrgTests.map((test) => test.testId)
          const batchSize = 50 // Smaller batch size to avoid rate limits
          let filteredTestIds = []

          for (let i = 0; i < testIds.length; i += batchSize) {
            const batch = testIds.slice(i, i + batchSize)

            const labTestQuery = `SELECT c.id, c.CLASS FROM c WHERE c.id IN (${batch
              .map((id) => `"${id}"`)
              .join(', ')})`

            try {
              const labTests = await cosmosDbContext.queryItems(
                labTestQuery,
                labTestContainer,
              )

              // Filter by department
              if (department === 'Others') {
                // For "Others" department, get tests that don't belong to mapped departments
                const mappedClasses = Object.keys(classToDepartmentMapping)
                const batchFiltered = labTests
                  .filter((test) => !mappedClasses.includes(test.CLASS))
                  .map((test) => test.id)
                filteredTestIds.push(...batchFiltered)
              } else {
                // For mapped departments, get tests that belong to those classes
                const departmentClasses = Object.entries(
                  classToDepartmentMapping,
                )
                  .filter(([, dept]) => dept === department)
                  .map(([classCode]) => classCode)

                const batchFiltered = labTests
                  .filter((test) => departmentClasses.includes(test.CLASS))
                  .map((test) => test.id)
                filteredTestIds.push(...batchFiltered)
              }

              // Add delay between batches to avoid rate limiting
              if (i + batchSize < testIds.length) {
                await new Promise((resolve) => setTimeout(resolve, 500))
              }
            } catch (error) {
              if (error.code === 429) {
                // Rate limit hit, wait longer and retry
                logging.logWarn(
                  `Rate limit hit, waiting 2 seconds before retry...`,
                )
                await new Promise((resolve) => setTimeout(resolve, 2000))
                i -= batchSize // Retry the same batch
                continue
              }
              throw error
            }
          }

          testsToRemove = filteredTestIds
        }
      } else if (selectAll) {
        // Get all tests for the organization
        const allOrgTestsQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
        const allOrgTests = await cosmosDbContext.queryItems(
          allOrgTestsQuery,
          organizationTestsContainer,
        )
        testsToRemove = allOrgTests.map((test) => test.testId)
      }

      if (!testsToRemove || testsToRemove.length === 0) {
        return removedTests
      }

      // Get existing organization tests to remove
      const existingTestsQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}" AND c.testId IN (${testsToRemove
        .map((id) => `"${id}"`)
        .join(', ')})`
      const existingTests = await cosmosDbContext.queryItems(
        existingTestsQuery,
        organizationTestsContainer,
      )

      // Remove tests in batches
      const batchSize = 100
      for (let i = 0; i < existingTests.length; i += batchSize) {
        const batch = existingTests.slice(i, i + batchSize)

        await Promise.all(
          batch.map(async (test) => {
            await cosmosDbContext.deleteItem(
              test.id,
              test.id,
              organizationTestsContainer,
            )
            removedTests.push(test.testId)
          }),
        )

        // Add delay between batches to avoid rate limiting
        if (i + batchSize < existingTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200))
        }
      }

      logging.logInfo(
        `Removed ${removedTests.length} tests from organization ${organizationId}`,
      )
      return removedTests
    } catch (error) {
      logging.logError('Error removing organization test details', error)
      throw new Error('Failed to remove organization test details')
    }
  }

  // Async version with progress tracking
  async removeOrganizationTestsAsync(
    organizationId,
    tests,
    department = null,
    selectAll = false,
    progressCallback = null,
  ) {
    try {
      const removedTests = []
      let testsToRemove = tests

      if (progressCallback) {
        progressCallback(0, 0, 'Determining tests to remove...')
      }

      if (selectAll && department) {
        if (progressCallback) {
          progressCallback(
            0,
            0,
            'Fetching organization tests for department...',
          )
        }

        // Get all organization tests first with smaller batches to avoid rate limiting
        const allOrgTestsQuery = `SELECT c.testId FROM c WHERE c.organizationId = "${organizationId}"`
        const allOrgTests = await cosmosDbContext.queryItems(
          allOrgTestsQuery,
          organizationTestsContainer,
        )

        if (allOrgTests.length === 0) {
          testsToRemove = []
        } else {
          if (progressCallback) {
            progressCallback(
              0,
              allOrgTests.length,
              `Found ${allOrgTests.length} organization tests, filtering by department...`,
            )
          }

          // Process test IDs in smaller batches to avoid rate limiting
          const testIds = allOrgTests.map((test) => test.testId)
          const batchSize = 50 // Smaller batch size to avoid rate limits
          let filteredTestIds = []

          for (let i = 0; i < testIds.length; i += batchSize) {
            const batch = testIds.slice(i, i + batchSize)

            if (progressCallback) {
              progressCallback(
                i,
                testIds.length,
                `Processing batch ${
                  Math.floor(i / batchSize) + 1
                } of ${Math.ceil(testIds.length / batchSize)}...`,
              )
            }

            const labTestQuery = `SELECT c.id, c.CLASS FROM c WHERE c.id IN (${batch
              .map((id) => `"${id}"`)
              .join(', ')})`

            try {
              const labTests = await cosmosDbContext.queryItems(
                labTestQuery,
                labTestContainer,
              )

              // Filter by department
              if (department === 'Others') {
                // For "Others" department, get tests that don't belong to mapped departments
                const mappedClasses = Object.keys(classToDepartmentMapping)
                const batchFiltered = labTests
                  .filter((test) => !mappedClasses.includes(test.CLASS))
                  .map((test) => test.id)
                filteredTestIds.push(...batchFiltered)
              } else {
                // For mapped departments, get tests that belong to those classes
                const departmentClasses = Object.entries(
                  classToDepartmentMapping,
                )
                  .filter(([, dept]) => dept === department)
                  .map(([classCode]) => classCode)

                const batchFiltered = labTests
                  .filter((test) => departmentClasses.includes(test.CLASS))
                  .map((test) => test.id)
                filteredTestIds.push(...batchFiltered)
              }

              // Add delay between batches to avoid rate limiting
              if (i + batchSize < testIds.length) {
                await new Promise((resolve) => setTimeout(resolve, 500))
              }
            } catch (error) {
              if (error.code === 429) {
                // Rate limit hit, wait longer and retry
                logging.logWarn(
                  `Rate limit hit, waiting 2 seconds before retry...`,
                )
                await new Promise((resolve) => setTimeout(resolve, 2000))
                i -= batchSize // Retry the same batch
                continue
              }
              throw error
            }
          }

          testsToRemove = filteredTestIds
        }
      } else if (selectAll) {
        // Get all tests for the organization
        const allOrgTestsQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
        const allOrgTests = await cosmosDbContext.queryItems(
          allOrgTestsQuery,
          organizationTestsContainer,
        )
        testsToRemove = allOrgTests.map((test) => test.testId)
      }

      if (!testsToRemove || testsToRemove.length === 0) {
        if (progressCallback) {
          progressCallback(0, 0, 'No tests to remove')
        }
        return {
          totalProcessed: 0,
          removedTests: [],
          message: 'No tests found to remove for the specified criteria',
        }
      }

      if (progressCallback) {
        progressCallback(
          0,
          testsToRemove.length,
          `Found ${testsToRemove.length} tests to remove`,
        )
      }

      // Get existing organization tests to remove
      const existingTestsQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}" AND c.testId IN (${testsToRemove
        .map((id) => `"${id}"`)
        .join(', ')})`
      const existingTests = await cosmosDbContext.queryItems(
        existingTestsQuery,
        organizationTestsContainer,
      )

      // Remove tests in smaller batches with better rate limiting handling
      const batchSize = 25 // Reduced batch size to avoid rate limits
      let processedCount = 0

      for (let i = 0; i < existingTests.length; i += batchSize) {
        const batch = existingTests.slice(i, i + batchSize)

        if (progressCallback) {
          progressCallback(
            processedCount,
            existingTests.length,
            `Removing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(
              existingTests.length / batchSize,
            )}`,
          )
        }

        try {
          // Process deletions sequentially to avoid overwhelming Cosmos DB
          for (const test of batch) {
            await cosmosDbContext.deleteItem(
              test.id,
              test.id,
              organizationTestsContainer,
            )
            removedTests.push(test.testId)

            // Small delay between individual deletions
            await new Promise((resolve) => setTimeout(resolve, 50))
          }

          processedCount += batch.length

          // Update progress after each batch
          if (progressCallback) {
            progressCallback(
              processedCount,
              existingTests.length,
              `Removed ${processedCount}/${existingTests.length} tests`,
            )
          }

          // Longer delay between batches to avoid rate limiting
          if (i + batchSize < existingTests.length) {
            await new Promise((resolve) => setTimeout(resolve, 1000))
          }
        } catch (error) {
          if (error.code === 429) {
            // Rate limit hit, wait longer and retry the batch
            logging.logWarn(
              `Rate limit hit during deletion, waiting 3 seconds before retry...`,
            )
            await new Promise((resolve) => setTimeout(resolve, 3000))
            i -= batchSize // Retry the same batch
            continue
          }
          throw error
        }
      }

      if (progressCallback) {
        progressCallback(
          existingTests.length,
          existingTests.length,
          `Successfully removed ${removedTests.length} tests`,
        )
      }

      logging.logInfo(
        `Removed ${removedTests.length} tests from organization ${organizationId}`,
      )

      return {
        totalProcessed: removedTests.length,
        removedTests: removedTests,
        message: `Successfully removed ${removedTests.length} tests`,
      }
    } catch (error) {
      logging.logError('Error removing organization test details', error)
      if (progressCallback) {
        progressCallback(0, 0, `Error: ${error.message}`)
      }
      throw new Error('Failed to remove organization test details')
    }
  }

  // Optimized method to fetch only active organization tests
  async fetchActiveOrganizationTests(
    organizationId,
    searchText,
    department,
    pageSize,
    page,
  ) {
    try {
      // Convert department name to class code if needed for filtering
      let targetClass = null
      if (department && department !== 'ALL') {
        const {
          getDepartmentByClass,
        } = require('../common/class-department-mapping')
        const classCode = getDepartmentByClass(department)
        targetClass = classCode || department
      }

      // Step 1: Get active organization tests with efficient query
      let orgQuery = `SELECT c.testId, c.price FROM c WHERE c.organizationId = "${organizationId}" AND c.isActive = true`

      const orgTests = await cosmosDbContext.queryItems(
        orgQuery,
        organizationTestsContainer,
      )

      if (orgTests.length === 0) {
        return {
          items: [],
          continuationToken: null,
          hasMoreResults: false,
          currentPage: page,
          pageSize: pageSize,
          totalFetched: 0,
          totalCount: 0,
          totalPages: 0,
        }
      }

      // Step 2: Get lab test details for these test IDs with optimized query
      const testIds = orgTests.map((test) => test.testId)
      const labTestQuery = `SELECT c.id, c.CLASS, c.SHORTNAME, c.LONG_COMMON_NAME, c.DisplayName FROM c WHERE c.id IN (${testIds
        .map((id) => `"${id}"`)
        .join(', ')})`

      const labTests = await cosmosDbContext.queryItems(
        labTestQuery,
        labTestContainer,
      )

      // Step 3: Create efficient lookup map for organization data
      const orgTestMap = new Map(orgTests.map((test) => [test.testId, test]))

      // Step 4: Apply filtering and combine data
      let filteredLabTests = labTests

      // Apply department filtering
      if (targetClass) {
        filteredLabTests = filteredLabTests.filter(
          (labTest) => labTest.CLASS === targetClass,
        )
      }

      // Apply search filtering
      if (searchText) {
        const searchUpper = searchText.toUpperCase()
        filteredLabTests = filteredLabTests.filter((labTest) => {
          return (
            labTest.id.toUpperCase().includes(searchUpper) ||
            (labTest.SHORTNAME &&
              labTest.SHORTNAME.toUpperCase().includes(searchUpper)) ||
            (labTest.LONG_COMMON_NAME &&
              labTest.LONG_COMMON_NAME.toUpperCase().includes(searchUpper)) ||
            (labTest.DisplayName &&
              labTest.DisplayName.toUpperCase().includes(searchUpper))
          )
        })
      }

      const totalCount = filteredLabTests.length

      if (totalCount === 0) {
        return {
          items: [],
          continuationToken: null,
          hasMoreResults: false,
          currentPage: page,
          pageSize: pageSize,
          totalFetched: 0,
          totalCount: 0,
          totalPages: 0,
        }
      }

      // Step 5: Apply pagination
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedTests = filteredLabTests.slice(startIndex, endIndex + 1)

      const hasMoreResults = paginatedTests.length > pageSize
      const actualResults = hasMoreResults
        ? paginatedTests.slice(0, pageSize)
        : paginatedTests
      const nextPageToken = hasMoreResults ? `page_${page + 1}` : null

      // Step 6: Transform results
      const transformedItems = actualResults.map((labTest) => {
        const orgTest = orgTestMap.get(labTest.id) || {}
        return {
          loincNum: labTest.id,
          class: labTest.CLASS || '',
          shortName: labTest.SHORTNAME || '',
          longCommonName: labTest.LONG_COMMON_NAME || '',
          displayName: labTest.DisplayName || '',
          isActive: true,
          cost: 0,
          organizationCost: orgTest.price || 0,
        }
      })

      return {
        items: transformedItems,
        continuationToken: nextPageToken,
        hasMoreResults: hasMoreResults,
        currentPage: page,
        pageSize: pageSize,
        totalFetched: transformedItems.length,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      }
    } catch (error) {
      logging.logError('Error fetching active organization tests:', error)
      // Fallback to simpler method if join query fails
      return await this.fetchActiveOrganizationTestsSimple(
        organizationId,
        searchText,
        department,
        pageSize,
        page,
      )
    }
  }

  // Optimized method to fetch tests that are NOT in organization container (inactive)
  async fetchInactiveTests(
    organizationId,
    searchText,
    department,
    pageSize,
    page,
  ) {
    try {
      // Convert department name to class code if needed for filtering
      let targetClass = null
      if (department && department !== 'ALL') {
        const {
          getDepartmentByClass,
        } = require('../common/class-department-mapping')
        const classCode = getDepartmentByClass(department)
        targetClass = classCode || department
      }

      // Step 1: Get organization test IDs efficiently
      const orgTestsQuery = `SELECT c.testId FROM c WHERE c.organizationId = "${organizationId}"`
      const orgTests = await cosmosDbContext.queryItems(
        orgTestsQuery,
        organizationTestsContainer,
      )
      const orgTestIds = new Set(orgTests.map((test) => test.testId))

      // Step 2: Build lab test query with filtering
      let labTestQuery = `SELECT c.id, c.CLASS, c.SHORTNAME, c.LONG_COMMON_NAME, c.DisplayName FROM c`
      const conditions = []

      // Add department filtering at database level
      if (targetClass) {
        conditions.push(`c.CLASS = "${targetClass}"`)
      }

      // Add search filtering at database level
      if (searchText) {
        const searchUpper = searchText.toUpperCase()
        conditions.push(`(
          CONTAINS(UPPER(c.id), "${searchUpper}") OR
          CONTAINS(UPPER(c.SHORTNAME), "${searchUpper}") OR
          CONTAINS(UPPER(c.LONG_COMMON_NAME), "${searchUpper}") OR
          CONTAINS(UPPER(c.DisplayName), "${searchUpper}")
        )`)
      }

      if (conditions.length > 0) {
        labTestQuery += ` WHERE ${conditions.join(' AND ')}`
      }

      // Step 3: Get all matching lab tests
      const allLabTests = await cosmosDbContext.queryItems(
        labTestQuery,
        labTestContainer,
      )

      // Step 4: Filter out organization tests in memory (more efficient than NOT EXISTS in Cosmos DB)
      const inactiveTests = allLabTests.filter(
        (test) => !orgTestIds.has(test.id),
      )

      const totalCount = inactiveTests.length

      if (totalCount === 0) {
        return {
          items: [],
          continuationToken: null,
          hasMoreResults: false,
          currentPage: page,
          pageSize: pageSize,
          totalFetched: 0,
          totalCount: 0,
          totalPages: 0,
        }
      }

      // Step 5: Apply pagination
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedTests = inactiveTests.slice(startIndex, endIndex + 1)

      const hasMoreResults = paginatedTests.length > pageSize
      const actualResults = hasMoreResults
        ? paginatedTests.slice(0, pageSize)
        : paginatedTests
      const nextPageToken = hasMoreResults ? `page_${page + 1}` : null

      // Step 6: Transform results
      const transformedItems = actualResults.map((labTest) => ({
        loincNum: labTest.id,
        class: labTest.CLASS || '',
        shortName: labTest.SHORTNAME || '',
        longCommonName: labTest.LONG_COMMON_NAME || '',
        displayName: labTest.DisplayName || '',
        isActive: false,
        cost: 0,
        organizationCost: 0,
      }))

      return {
        items: transformedItems,
        continuationToken: nextPageToken,
        hasMoreResults: hasMoreResults,
        currentPage: page,
        pageSize: pageSize,
        totalFetched: transformedItems.length,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      }
    } catch (error) {
      logging.logError('Error fetching inactive tests:', error)
      // Fallback to simpler method if optimized query fails
      return await this.fetchInactiveTestsSimple(
        organizationId,
        searchText,
        department,
        pageSize,
        page,
      )
    }
  }

  // Simple fallback method for active organization tests (original logic)
  async fetchActiveOrganizationTestsSimple(
    organizationId,
    searchText,
    department,
    pageSize,
    page,
  ) {
    try {
      // Get active organization tests
      const orgQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}" AND c.isActive = true`
      const orgTests = await cosmosDbContext.queryItems(
        orgQuery,
        organizationTestsContainer,
      )

      if (orgTests.length === 0) {
        return {
          items: [],
          continuationToken: null,
          hasMoreResults: false,
          currentPage: page,
          pageSize: pageSize,
          totalFetched: 0,
          totalCount: 0,
          totalPages: 0,
        }
      }

      // Get lab test details
      const testIds = orgTests.map((test) => test.testId)
      const labTestQuery = `SELECT * FROM c WHERE c.id IN (${testIds
        .map((id) => `"${id}"`)
        .join(', ')})`
      const labTests = await cosmosDbContext.queryItems(
        labTestQuery,
        labTestContainer,
      )

      const labTestMap = new Map(labTests.map((test) => [test.id, test]))

      // Apply filtering
      let filteredOrgTests = orgTests

      if (department && department !== 'ALL') {
        const {
          getDepartmentByClass,
        } = require('../common/class-department-mapping')
        const classCode = getDepartmentByClass(department)
        const targetClass = classCode || department

        filteredOrgTests = filteredOrgTests.filter((orgTest) => {
          const labTest = labTestMap.get(orgTest.testId)
          return labTest && labTest.CLASS === targetClass
        })
      }

      if (searchText) {
        filteredOrgTests = filteredOrgTests.filter((orgTest) => {
          const labTest = labTestMap.get(orgTest.testId)
          if (!labTest) return false

          const searchUpper = searchText.toUpperCase()
          return (
            orgTest.testId.toUpperCase().includes(searchUpper) ||
            (labTest.SHORTNAME &&
              labTest.SHORTNAME.toUpperCase().includes(searchUpper)) ||
            (labTest.LONG_COMMON_NAME &&
              labTest.LONG_COMMON_NAME.toUpperCase().includes(searchUpper)) ||
            (labTest.DisplayName &&
              labTest.DisplayName.toUpperCase().includes(searchUpper))
          )
        })
      }

      // Apply pagination
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedOrgTests = filteredOrgTests.slice(startIndex, endIndex + 1)

      const hasMoreResults = paginatedOrgTests.length > pageSize
      const actualResults = hasMoreResults
        ? paginatedOrgTests.slice(0, pageSize)
        : paginatedOrgTests
      const nextPageToken = hasMoreResults ? `page_${page + 1}` : null

      const transformedItems = actualResults.map((orgTest) => {
        const labTest = labTestMap.get(orgTest.testId) || {}
        return {
          loincNum: orgTest.testId,
          class: labTest.CLASS || '',
          shortName: labTest.SHORTNAME || '',
          longCommonName: labTest.LONG_COMMON_NAME || '',
          displayName: labTest.DisplayName || '',
          isActive: true,
          cost: 0,
          organizationCost: orgTest.price || 0,
        }
      })

      return {
        items: transformedItems,
        continuationToken: nextPageToken,
        hasMoreResults: hasMoreResults,
        currentPage: page,
        pageSize: pageSize,
        totalFetched: transformedItems.length,
        totalCount: filteredOrgTests.length,
        totalPages: Math.ceil(filteredOrgTests.length / pageSize),
      }
    } catch (error) {
      logging.logError('Error in fallback active organization tests:', error)
      return {
        items: [],
        continuationToken: null,
        hasMoreResults: false,
        currentPage: page,
        pageSize: pageSize,
        totalFetched: 0,
        totalCount: 0,
        totalPages: 0,
      }
    }
  }

  // Simple fallback method for inactive tests (original logic)
  async fetchInactiveTestsSimple(
    organizationId,
    searchText,
    department,
    pageSize,
    page,
  ) {
    try {
      // Get organization test IDs
      const orgTestsQuery = `SELECT c.testId FROM c WHERE c.organizationId = "${organizationId}"`
      const orgTests = await cosmosDbContext.queryItems(
        orgTestsQuery,
        organizationTestsContainer,
      )
      const orgTestIds = new Set(orgTests.map((test) => test.testId))

      // Build lab test query
      let labTestQuery = 'SELECT * FROM c'
      const conditions = []

      if (searchText) {
        conditions.push(
          `(CONTAINS(UPPER(c.id), UPPER("${searchText}")) OR CONTAINS(UPPER(c.SHORTNAME), UPPER("${searchText}")) OR CONTAINS(UPPER(c.LONG_COMMON_NAME), UPPER("${searchText}")))`,
        )
      }

      if (department && department !== 'ALL') {
        const {
          getDepartmentByClass,
        } = require('../common/class-department-mapping')
        const classCode = getDepartmentByClass(department)
        const targetClass = classCode || department
        conditions.push(`c.CLASS = "${targetClass}"`)
      }

      if (conditions.length > 0) {
        labTestQuery += ` WHERE ${conditions.join(' AND ')}`
      }

      const allLabTests = await cosmosDbContext.queryItems(
        labTestQuery,
        labTestContainer,
      )

      // Filter out organization tests
      const inactiveTests = allLabTests.filter(
        (test) => !orgTestIds.has(test.id),
      )

      // Apply pagination
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedTests = inactiveTests.slice(startIndex, endIndex + 1)

      const hasMoreResults = paginatedTests.length > pageSize
      const actualResults = hasMoreResults
        ? paginatedTests.slice(0, pageSize)
        : paginatedTests
      const nextPageToken = hasMoreResults ? `page_${page + 1}` : null

      const transformedItems = actualResults.map((labTest) => ({
        loincNum: labTest.id,
        class: labTest.CLASS || '',
        shortName: labTest.SHORTNAME || '',
        longCommonName: labTest.LONG_COMMON_NAME || '',
        displayName: labTest.DisplayName || '',
        isActive: false,
        cost: 0,
        organizationCost: 0,
      }))

      return {
        items: transformedItems,
        continuationToken: nextPageToken,
        hasMoreResults: hasMoreResults,
        currentPage: page,
        pageSize: pageSize,
        totalFetched: transformedItems.length,
        totalCount: inactiveTests.length,
        totalPages: Math.ceil(inactiveTests.length / pageSize),
      }
    } catch (error) {
      logging.logError('Error in fallback inactive tests:', error)
      return {
        items: [],
        continuationToken: null,
        hasMoreResults: false,
        currentPage: page,
        pageSize: pageSize,
        totalFetched: 0,
        totalCount: 0,
        totalPages: 0,
      }
    }
  }
}

module.exports = new TestRepository()
